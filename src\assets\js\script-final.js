/*
* Canabro - Medical Marijuana Dispensary HTML Template
* Main JavaScript File
*/

(function($) {
    "use strict";

    // Preloader
    $(window).on('load', function() {
        $('#preloader').fadeOut(1000);
    });

    // Mobile Menu
    $('.mobile-nav-toggler').on('click', function(e) {
        e.preventDefault();
        $('.mobile-menu').addClass('active');
        $('.menu-backdrop').addClass('active');
        $('body').addClass('menu-open');
    });

    $('.close-btn, .menu-backdrop').on('click', function(e) {
        e.preventDefault();
        $('.mobile-menu').removeClass('active');
        $('.menu-backdrop').removeClass('active');
        $('body').removeClass('menu-open');
    });

    // Close mobile menu when clicking on menu links (except dropdown toggles)
    $('.mobile-menu .navigation li:not(.dropdown) > a').on('click', function() {
        $('.mobile-menu').removeClass('active');
        $('.menu-backdrop').removeClass('active');
        $('body').removeClass('menu-open');
    });

    // Close mobile menu with Escape key
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape' && $('.mobile-menu').hasClass('active')) {
            $('.mobile-menu').removeClass('active');
            $('.menu-backdrop').removeClass('active');
            $('body').removeClass('menu-open');
        }
    });

    // Dropdown Toggle for Mobile Menu (Multi-level support)
    $('.mobile-menu .navigation li.dropdown > a').on('click touchstart', function(e) {
        e.preventDefault();
        e.stopPropagation();
        var $this = $(this);
        var $parent = $this.parent();
        var $submenu = $this.next('ul');

        // Toggle current dropdown
        $submenu.slideToggle(300);
        $parent.toggleClass('active');

        // Close other dropdowns at the same level (but not nested ones)
        $parent.siblings('.dropdown').removeClass('active').children('ul').slideUp(300);

        // Also close nested dropdowns when parent is closed
        if (!$parent.hasClass('active')) {
            $submenu.find('.dropdown').removeClass('active');
            $submenu.find('ul').slideUp(300);
        }
    });

    // Desktop dropdown hover effects
    $('.navigation > li.dropdown').hover(
        function() {
            $(this).find('> ul').stop(true, true).fadeIn(300);
        },
        function() {
            $(this).find('> ul').stop(true, true).fadeOut(300);
        }
    );

    // Search Popup
    $('.search-toggle').on('click', function(e) {
        e.preventDefault();
        $('.search-popup').addClass('active');
    });

    $('.close-search').on('click', function() {
        $('.search-popup').removeClass('active');
    });

    // Main Slider
    if ($('.main-slider-carousel').length) {
        $('.main-slider-carousel').owlCarousel({
            loop: true,
            margin: 0,
            nav: true,
            dots: true,
            animateOut: 'fadeOut',
            animateIn: 'fadeIn',
            active: true,
            smartSpeed: 1000,
            autoplay: true,
            autoplayTimeout: 7000,
            navText: ['<i class="fas fa-angle-left"></i>', '<i class="fas fa-angle-right"></i>'],
            responsive: {
                0: {
                    items: 1
                },
                600: {
                    items: 1
                },
                800: {
                    items: 1
                },
                1024: {
                    items: 1
                }
            }
        });
    }

    // Testimonial Carousel
    if ($('.testimonial-carousel').length) {
        $('.testimonial-carousel').owlCarousel({
            loop: true,
            margin: 30,
            nav: false,
            dots: true,
            smartSpeed: 500,
            autoplay: true,
            autoplayTimeout: 5000,
            responsive: {
                0: {
                    items: 1
                },
                600: {
                    items: 1
                },
                768: {
                    items: 2
                },
                992: {
                    items: 3
                }
            }
        });
    }

    // Clients Carousel
    if ($('.clients-carousel').length) {
        $('.clients-carousel').owlCarousel({
            loop: true,
            margin: 30,
            nav: false,
            dots: false,
            smartSpeed: 500,
            autoplay: true,
            autoplayTimeout: 3000,
            responsive: {
                0: {
                    items: 2
                },
                600: {
                    items: 3
                },
                768: {
                    items: 4
                },
                992: {
                    items: 5
                }
            }
        });
    }

    // Product Filter
    if ($('.product-filter').length) {
        $('.product-filter li').on('click', function() {
            $('.product-filter li').removeClass('active');
            $(this).addClass('active');

            var selector = $(this).attr('data-filter');

            if (selector === '*') {
                $('.product-item').show();
            } else {
                $('.product-item').hide();
                $(selector).show();
            }
        });
    }

    // Back to Top
    if ($('.back-to-top').length) {
        var scrollTrigger = 100, // px
            backToTop = function() {
                var scrollTop = $(window).scrollTop();
                if (scrollTop > scrollTrigger) {
                    $('.back-to-top').addClass('active');
                } else {
                    $('.back-to-top').removeClass('active');
                }
            };

        backToTop();

        $(window).on('scroll', function() {
            backToTop();
        });

        $('.back-to-top').on('click', function(e) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: 0
            }, 700);
        });
    }

    // Age Verification Popup
    if ($('#ageVerification').length) {
        // Check if user has already verified age
        if (!localStorage.getItem('ageVerified')) {
            $('#ageVerification').fadeIn();
        }

        // Yes button
        $('#ageYes').on('click', function() {
            localStorage.setItem('ageVerified', 'true');
            $('#ageVerification').fadeOut();
        });

        // No button
        $('#ageNo').on('click', function() {
            window.location.href = 'https://www.google.com';
        });
    }

    // Newsletter Popup
    if ($('#newsletterPopup').length) {
        // Check if user has already closed the popup
        if (!sessionStorage.getItem('newsletterClosed')) {
            setTimeout(function() {
                $('#newsletterPopup').fadeIn();
            }, 5000);
        }

        // Close button
        $('#closeNewsletter').on('click', function() {
            sessionStorage.setItem('newsletterClosed', 'true');
            $('#newsletterPopup').fadeOut();
        });

        // Form submission
        $('#newsletterForm').on('submit', function(e) {
            e.preventDefault();
            sessionStorage.setItem('newsletterClosed', 'true');
            $('#newsletterPopup').fadeOut();
            alert('Thank you for subscribing!');
        });
    }

    // Sticky Header
    if ($('.header').length) {
        var lastScrollTop = 0;
        var headerHeight = $('.header-top').outerHeight() || 100;
        var delta = 5;

        $(window).on('scroll', function() {
            var scrollPos = $(window).scrollTop();

            // Make sure they scroll more than delta
            if(Math.abs(lastScrollTop - scrollPos) <= delta)
                return;

            if (scrollPos > headerHeight) {
                // If scrolling down and past the threshold
                $('.header').addClass('fixed-header');
                $('body').addClass('fixed-header-padding');

                // Always show the header when it's fixed
                $('.header.fixed-header').css('transform', 'translateY(0)');
            } else {
                // At the top
                $('.header').removeClass('fixed-header');
                $('body').removeClass('fixed-header-padding');
                $('.header').css('transform', 'translateY(0)');
            }

            lastScrollTop = scrollPos;
        });
    }

})(jQuery);
